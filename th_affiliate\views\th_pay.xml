<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="th_pay_view" model="ir.ui.view">
        <field name="name">th.pay</field>
        <field name="model">th.pay</field>
        <field name="arch" type="xml">
            <tree string="">
                <field name="name"/>
                <field name="th_partner_id"/>
                <field name="th_paid_date"/>
                <field name="th_currency_id" invisible="1"/>
                <field name="th_paid" widget="monetary" options="{'currency_field': 'th_currency_id'}" />
            </tree>
        </field>
    </record>

    <record id="th_pay_form_view" model="ir.ui.view">
        <field name="name">th.pay.form</field>
        <field name="model">th.pay</field>
        <field name="arch" type="xml">
            <form string="">
                <header>
                    <button name="action_pending_pay" class="btn" type="object" groups="base.group_no_one" string="Chờ duyệt" states="pending,accept,paid,cancel"/>
                    <button name="action_cancel_pay" class="btn" type="object" string="Hủy" confirm="Bạn có muốn hủy phiếu thanh toán này?" groups="th_affiliate.group_aff_manager" states="pending,accept"/>
                    <button name="action_accept_pay" class="btn btn-primary" type="object" confirm="Bạn có muốn duyệt phiếu thanh toán này?" groups="th_affiliate.group_aff_manager" string="Duyệt &amp; chờ thanh toán" states="pending"/>
                    <button name="th_action_paid" class="btn btn-primary" type="object" confirm="Bạn có muốn thanh toán cho phiếu này?" groups="th_affiliate.group_aff_manager" string="Thanh toán" states="accept"/>
                    <field name="state" widget="statusbar" statusbar_visible="pending,accept,paid"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name" attrs="{'readonly': [('state', '!=', 'pending')]}"/>
                            <field name="th_partner_id" readonly="1"/>
                        </group>
                        <group>
                            <field name="th_count_correct_link" readonly="1"/>
                            <field name="th_currency_id" invisible="1"/>
                            <field name="th_paid" readonly="1"  widget="monetary" options="{'currency_field': 'th_currency_id'}" />
                            <field name="th_paid_date" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Thanh toán link">
<!--                            options="{'delete': [('state','=', 'pending')]}"-->
                            <field name="th_post_link_ids">
                                <tree string="Record post link" class="th_post_link" create="0" delete="0" no_open="True"  editable="bottom">
                                    <field name="th_campaign_id" string="Chiến dịch"/>
                                    <field name="id"/>
                                    <button name="action_visit_page" type="object" string="Link" icon="fa-external-link"/>
                                    <field name="th_acceptance_person_id"/>
                                    <field name="create_date" string="Ngày tạo" widget="date"/>
                                    <field name="th_pay_state" invisible="1"/>
                                    <field name="state" groups="th_affiliate.group_aff_manager" attrs="{'readonly': [('th_pay_state', '!=', 'pending')]}"/>
                                    <field name="th_pricelist_ids" widget="many2many_tags" groups="th_affiliate.group_aff_manager" attrs="{'readonly': [('th_pay_state', '!=', 'pending')]}"/>
<!--                                    <field name="th_expense"/>-->
                                    <field name="th_state_pay" invisible="1"/>
                                    <field name="th_unit_price"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Thanh toán lead">
                            <field name="th_opportunity_ctv_ids"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="th_pay_view_search" model="ir.ui.view">
        <field name="name">th.pay.view.search</field>
        <field name="model">th.pay</field>
        <field name="arch" type="xml">
            <search string="">
                <filter string="Ngày tạo" name="create_date" date="create_date"/>
                <filter string="Ngày chi Trả" name="th_paid_date" date="th_paid_date"/>
                <searchpanel>
                    <field name="state" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="th_pay_action" model="ir.actions.act_window">
        <field name="name">Phiếu thanh toán</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.pay</field>
        <field name="search_view_id" ref="th_pay_view_search"/>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
          <p class="oe_view_nocontent_create">
            <!-- Add Text Here -->
          </p><p>
            <!-- More details about what a user can do with this object will be OK -->
          </p>
        </field>
    </record>
</odoo>