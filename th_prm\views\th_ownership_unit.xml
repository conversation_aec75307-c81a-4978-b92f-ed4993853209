<odoo>
    <record id="prm_lead_ownership_unit_view_kanban" model="ir.ui.view">
        <field name="name">prm_lead_ownership_unit_view_kanban</field>
        <field name="model">th.ownership.unit</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="th_description"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                            <a class="o_crm_kanban_boxes" name="action_prm_lead_ownership_unit" type="object">
                                <span class="oe_kanban_color_help"
                                      t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                      t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                                <div t-attf-class="o_kanban_card_header">
                                    <div class="o_kanban_card_header_title">
                                        <div class="o_kanban_record_title oe_kanban_details">
                                            <strong>
                                                <h4>
                                                    <field name="name"/>
                                                </h4>
                                            </strong>
                                        </div>
                                        <div t-if="record.th_description">
                                            <i class="fa fa-info-circle" title="Description" role="img"
                                               aria-label="Description"></i>Description
                                            <t t-esc="record.th_description.value"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                            </a>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown"
                                   data-bs-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="pom_lead_ownership_unit_view_kanban" model="ir.ui.view">
        <field name="name">pom_lead_ownership_unit_view_kanban</field>
        <field name="model">th.ownership.unit</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="th_description"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                            <a class="o_crm_kanban_boxes" name="action_pom_lead_ownership_unit" type="object">
                                <span class="oe_kanban_color_help"
                                      t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                      t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                                <div t-attf-class="o_kanban_card_header">
                                    <div class="o_kanban_card_header_title">
                                        <div class="o_kanban_record_title oe_kanban_details">
                                            <strong>
                                                <h4>
                                                    <field name="name"/>
                                                </h4>
                                            </strong>
                                        </div>
                                        <div t-if="record.th_description">
                                            <i class="fa fa-info-circle" title="Description" role="img"
                                               aria-label="Description"></i>Description
                                            <t t-esc="record.th_description.value"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                            </a>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown"
                                   data-bs-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="prm_warehouse_ownership_unit_view_kanban" model="ir.ui.view">
        <field name="name">prm_warehouse_ownership_unit_view_kanban</field>
        <field name="model">th.ownership.unit</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="th_description"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                            <a class="o_crm_kanban_boxes" name="action_prm_lead_reuse" type="object">
                                <span class="oe_kanban_color_help"
                                      t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                      t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                                <div t-attf-class="o_kanban_card_header">
                                    <div class="o_kanban_card_header_title">
                                        <div class="o_kanban_record_title oe_kanban_details">
                                            <strong>
                                                <h4>
                                                    <field name="name"/>
                                                </h4>
                                            </strong>
                                        </div>
                                        <div t-if="record.th_description">
                                            <i class="fa fa-info-circle" title="Description" role="img"
                                               aria-label="Description"></i>Description
                                            <t t-esc="record.th_description.value"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                            </a>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown"
                                   data-bs-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="pom_warehouse_ownership_unit_view_kanban" model="ir.ui.view">
        <field name="name">pom_warehouse_ownership_unit_view_kanban</field>
        <field name="model">th.ownership.unit</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="th_description"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                            <a class="o_crm_kanban_boxes" name="action_pom_lead_reuse" type="object">
                                <span class="oe_kanban_color_help"
                                      t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                      t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                                <div t-attf-class="o_kanban_card_header">
                                    <div class="o_kanban_card_header_title">
                                        <div class="o_kanban_record_title oe_kanban_details">
                                            <strong>
                                                <h4>
                                                    <field name="name"/>
                                                </h4>
                                            </strong>
                                        </div>
                                        <div t-if="record.th_description">
                                            <i class="fa fa-info-circle" title="Description" role="img"
                                               aria-label="Description"></i>Description
                                            <t t-esc="record.th_description.value"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
                            </a>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown"
                                   data-bs-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="partner_profile_ownership_unit_view_kanban" model="ir.ui.view">
        <field name="name">partner_profile_ownership_unit_view_kanban</field>
        <field name="model">th.ownership.unit</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="name"/>
                <field name="th_description"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
<!--                            <a class="o_crm_kanban_boxes" name="action_partner_profile_ownership_unit" type="object">-->
                                <span class="oe_kanban_color_help"
                                      t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                      t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                                <div t-attf-class="o_kanban_card_header">
                                    <div class="o_kanban_card_header_title">
                                        <div class="o_kanban_record_title oe_kanban_details">
                                            <strong>
                                                <h4>
                                                    <field name="name"/>
                                                </h4>
                                            </strong>
                                        </div>
                                        <div t-if="record.th_description">
                                            <i class="fa fa-info-circle" title="Description" role="img"
                                               aria-label="Description"></i>Description
                                            <t t-esc="record.th_description.value"/>
                                        </div>
                                    </div>
                                    <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                        <a class="o_kanban_manage_toggle_button" href="#">
                                            <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                        </a>
                                    </div>
                                </div>
<!--                            </a>-->
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown"
                                   data-bs-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="prm_lead_ownership_unit_action" model="ir.actions.act_window">
        <field name="name">PRM</field>
        <field name="res_model">th.ownership.unit</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': False}</field>
        <field name="view_id" ref="prm_lead_ownership_unit_view_kanban"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>

    <record id="pom_lead_ownership_unit_action" model="ir.actions.act_window">
        <field name="name">POM</field>
        <field name="res_model">th.ownership.unit</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': False}</field>
        <field name="view_id" ref="pom_lead_ownership_unit_view_kanban"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>

    <record id="prm_warehouse_ownership_unit_action" model="ir.actions.act_window">
        <field name="name">Kho PRM</field>
        <field name="res_model">th.ownership.unit</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': False}</field>
        <field name="view_id" ref="prm_warehouse_ownership_unit_view_kanban"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>

    <record id="pom_warehouse_ownership_unit_action" model="ir.actions.act_window">
        <field name="name">Kho POM</field>
        <field name="res_model">th.ownership.unit</field>
        <field name="view_mode">kanban,form</field>
        <field name="context">{'create': False}</field>
        <field name="view_id" ref="pom_warehouse_ownership_unit_view_kanban"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>

<!--    <record id="partner_profile_ownership_unit_action" model="ir.actions.act_window">-->
<!--        <field name="name">Kho hồ sơ đối tác</field>-->
<!--        <field name="res_model">th.ownership.unit</field>-->
<!--        <field name="view_mode">kanban,form</field>-->
<!--        <field name="context">{'create': False}</field>-->
<!--        <field name="view_id" ref="partner_profile_ownership_unit_view_kanban"/>-->
<!--        <field name="help" type="html">-->
<!--            <p class="o_view_nocontent_smiling_face">-->
<!--                Chưa có dữ liệu-->
<!--            </p>-->
<!--        </field>-->
<!--    </record>-->
</odoo>