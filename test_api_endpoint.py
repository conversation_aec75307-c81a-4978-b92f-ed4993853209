#!/usr/bin/env python3
"""
Script to test FastAPI endpoint directly
"""
import requests
import json

def test_api_endpoint():
    # Thay đổi các giá trị này theo cấu hình của bạn
    BASE_URL = "http://localhost:8000"  # Thay đổi theo URL thực tế
    API_KEY = "your-api-key"  # Thay đổi theo API key thực tế
    
    # Test data
    test_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "login": "<EMAIL>",
        "phone": "0123456789",
        "th_affiliate_code": "TEST001",
        "th_customer_code": "CUST001",
        "th_citizen_identification": "123456789",
        "th_place_identification": "Ha Noi"
    }
    
    headers = {
        "api-key": API_KEY,
        "Content-Type": "application/json"
    }
    
    # Test các endpoint khác nhau
    endpoints_to_test = [
        "/api/users",
        "/api/v1/users", 
        "/sc/api/v1/users",
        "/users",
        "/v1/users"
    ]
    
    print("=== TESTING API ENDPOINTS ===")
    print(f"Base URL: {BASE_URL}")
    print(f"API Key: {API_KEY[:10]}..." if API_KEY else "No API Key")
    print()
    
    for endpoint in endpoints_to_test:
        url = BASE_URL + endpoint
        print(f"Testing: {url}")
        
        try:
            response = requests.post(url, json=test_data, headers=headers, timeout=10)
            print(f"  Status: {response.status_code}")
            print(f"  Response: {response.text[:200]}...")
        except requests.exceptions.ConnectionError:
            print(f"  ERROR: Connection failed - Server may not be running")
        except requests.exceptions.Timeout:
            print(f"  ERROR: Request timeout")
        except Exception as e:
            print(f"  ERROR: {e}")
        print()

    # Test base URL
    print("Testing base URL accessibility:")
    try:
        response = requests.get(BASE_URL, timeout=10)
        print(f"  Base URL Status: {response.status_code}")
        print(f"  Base URL Response: {response.text[:200]}...")
    except Exception as e:
        print(f"  Base URL ERROR: {e}")

if __name__ == "__main__":
    test_api_endpoint()
