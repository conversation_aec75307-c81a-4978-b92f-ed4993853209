<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <record id="prm_lead_view_search" model="ir.ui.view">
        <field name="name">prm.lead.search</field>
        <field name="model">prm.lead</field>
        <field name="arch" type="xml">
            <search>
                <field name="th_partner_phone"
                       filter_domain="[('th_partner_id.phone', 'ilike', self)]"/>
                <field name="th_partner_phone2"
                       filter_domain="['|', ('th_partner_id.phone', 'ilike', self), ('th_partner_id.th_phone2', 'ilike', self)]"/>
                <field name="th_partner_email" filter_domain="[('th_partner_id.email', 'ilike', self)]"/>
                <field name="th_partner_code" />
                <field name="th_customer_code_aum" invisible="1"/>
                <field name="th_user_id"/>
                <field name="name" string="<PERSON><PERSON><PERSON>ơ hội"/>
                <field name="name_id_sequence" string="Mã cơ hội"/>
                <field name="th_reuse_source"/>
                <field name="th_partner_group_ids"/>
                <field name="th_collaborative_products_ids"/>
                <field name="th_partner_reference_id"/>
                <field name="th_work_unit"/>
                <field name="th_partner_id" string="Tên đối tác"
                       filter_domain="[('th_partner_id.display_name','ilike',self)]"/>
                <field name="th_call_status_detail_id" string="Trạng thái chi tiết"
                       filter_domain="[('th_call_status_detail_id.name','ilike',self)]"/>
                <field name="th_contract_number"/>
                <separator/>
                <filter name="user_id" string="Chưa có người phụ trách" domain="[('th_user_id', '=', False)]"/>
                <filter name="lead_today" string="Cơ hội hôm nay"
                        domain="[('create_date', '&gt;', (context_today() - datetime.timedelta(days=1)).strftime('%Y-%m-%d'))]"/>
                <group>
                    <filter name="stage_id" string="Mối quan hệ" context="{'group_by':'th_stage_id'}"/>
                    <filter name="last_check" string="Liên hệ lần cuối" context="{'group_by':'th_last_check:day'}"/>
                    <filter name="last_check_1" string="Liên hệ cuối từ 1 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=0)))]"/>
                    <filter name="last_check_4" string="Liên hệ cuối từ 4 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=3)))]"/>
                    <filter name="last_check_15" string="Liên hệ cuối từ 15 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=14)))]"/>
                    <filter name="last_check_30" string="Liên hệ cuối từ 30 ngày trở lên"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=29)))]"/>
                    <filter name="last_check_0_to_45" string="Liên hệ cuối từ 0 đến 45 ngày"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=-1))),
                                     ('th_last_check', '&gt;', (context_today() - datetime.timedelta(days=46)))]"/>
                    <filter name="last_check_0_to_90" string="Liên hệ cuối từ 0 đến 90 ngày"
                            domain="[('th_last_check', '&lt;', (context_today() - datetime.timedelta(days=-1))),
                                     ('th_last_check', '&gt;', (context_today() - datetime.timedelta(days=91)))]"/>
                </group>
                <searchpanel>
                    <field name="th_stage_id" icon="fa-user-plus" enable_counters="1"/>
                    <field name="th_is_pom" icon="far fa-address-card" enable_counters="1"/>
                    <field name="th_call_status" icon="fa-phone" enable_counters="1"/>
                    <field name="th_call_status_detail_id" icon="fa-phone" enable_counters="1"/>
                    <field name="th_user_id" icon="fa-user-plus" enable_counters="1"/>
                </searchpanel>
            </search>
        </field>
    </record>

    <record id="prm_lead_view_form" model="ir.ui.view">
        <field name="name">prm.lead.form</field>
        <field name="model">prm.lead</field>
        <field name="arch" type="xml">
            <form js_class="custom_prm_form">
                <field name="th_first_status_stage" invisible="1"/>
                <field name="th_last_status_stage" invisible="1"/>
                <field name="th_stage_need_document" invisible="1"/>
                <field name="th_pom_lead_count" invisible="1"/>
                <field name="th_storage" invisible="1"/>
                <field name="th_check_aff" invisible="1"/>
                <header>
                    <button name="action_change_state_to_pom" string="Chuyển pom" type="object" class="oe_highlight"
                            attrs="{'invisible': ['|', '|', ('th_storage', '=', True), ('th_last_status_stage', '!=', True), ('th_pom_lead_count', '!=', 0)]}"
                            confirm="Bạn có chắc chắn chuyển trạng thái sang POM?"/>
                    <button name="action_api_create_user" string="Tạo tài khoản Affiliate" type="object"
                            class="oe_highlight"
                            attrs="{'invisible': ['|','|',('th_storage', '=', True), ('th_last_status_stage', '!=', True), ('th_check_aff', '!=', False)]}"
                            confirm="Bạn có chắc chắn muốn tạo tài khoản Affiliate?"/>
                    <button name="action_api_create_user_fast" string="Tạo tài khoản Affiliate bằng Fast" type="object"
                            class="oe_highlight"
                            attrs="{'invisible': ['|','|',('th_storage', '=', True), ('th_last_status_stage', '!=', True), ('th_check_aff', '!=', False)]}"
                            confirm="Bạn có chắc chắn muốn tạo tài khoản Affiliate?" groups="base.group_no_one"/>
                    <field name="th_stage_id" widget="th_statusbar" class="o_field_statusbar"
                           options="{'clickable': '1'}"
                           attrs="{'invisible': [('th_pom_lead_count', '!=', 0)]}"/>
                    <field name="th_stage_id" widget="statusbar"
                           attrs="{'invisible': [('th_pom_lead_count', '=', 0)]}"/>
                </header>
                <sheet>
                    <field name="active" invisible="1"/>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_profile" type="object" class="oe_stat_button"
                                icon="fa-address-card"
                                attrs="{'invisible': [('th_partner_id', '=', False)]}">
                            <div class="o_stat_info">
                                <span class="o_stat_text">
                                    Hồ Sơ
                                </span>
                            </div>
                        </button>
                        <button name="action_open_pom_lead" type="object" invisible="context.get('not_open_pom', False)"
                                attrs="{'invisible': [('th_pom_lead_count', '=', 0)]}"
                                class="oe_stat_button" icon="fa-id-card-o">
                            <div class="o_stat_info">
                                <span class="o_stat_value">
                                    <field name="th_pom_lead_count"/>
                                </span>
                                <span class="o_stat_text">POM</span>
                            </div>
                        </button>
                    </div>
                    <widget name="web_ribbon" title="Có cơ hội trùng"
                            attrs="{'invisible': [('th_duplicate_lead_formio', '=', False)]}"/>
                    <div class="oe_title">
                        <h1>
                            <field name="name" class="text-break" placeholder="VD: Cơ hội của Nguyễn Văn A"
                                   readonly="1"/>
                        </h1>
                        <h4 class="d-flex gap-2 g-0 pb-3">
                            <div style="width: 15%; min-width: 4rem;">
                                <label for="th_partner_code"/>
                                <br/>
                                <field name="th_partner_code"/>
<!--                                <label for="th_customer_code_aum"/>-->
<!--                                <br/>-->
<!--                                <field name="th_customer_code_aum" readonly="1"/>-->
                            </div>
                            <div style="width: 15%; min-width: 4rem;">
                                <label for="th_last_check" attrs="{'invisible': [('th_pom_lead_count', '>', 0)]}"/>
                                <br/>
                                <field name="th_last_check" widget="remaining_days" readonly="1"
                                       attrs="{'invisible': [('th_pom_lead_count', '>', 0)]}"/>
                            </div>
                        </h4>
                    </div>
                    <group col="3">
                        <group>
                            <field name="th_reuse_source" readonly="1"
                                   attrs="{'invisible': [('th_reuse_source', '=', False)]}"/>
                            <field name="th_check_phone" attrs="{'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_check_email" attrs="{'invisible': [('th_partner_id', '!=', False)]}"/>
                            <field name="th_partner_id" string="Tên đối tác *"
                                   options='{"no_open": True, "no_quick_create": True}' required="1"
                                   attrs="{'readonly': [('id', '!=', False)]}"
                                   context="{'default_phone': th_check_phone, 'default_email': th_check_email, 'default_th_module_ids': th_module_ids}"/>
                            <field name="th_partner_email" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_partner_phone" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_partner_phone2" attrs="{'invisible': [('th_partner_id', '=', False)]}"/>
                            <field name="th_user_id" options="{'no_create': True, 'no_open': True}" readonly="1"/>
                            <field name="th_ownership_unit_id" readonly="1"
                                   options="{'no_open': True, 'no_create': True}"/>
                            <field name="th_module_ids" widget="many2many_tags" invisible="1"/>
                        </group>
                        <group>
                            <field name="th_partner_group_ids" string="Nhóm đối tác *" required="1"
                                   widget="many2many_tags"
                                   options="{'no_create': True, 'color_field': 'th_color'}"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_partner_group_ids', '!=', False)]}"/>
                            <field name="th_partner_source_id" string="Nguồn đối tác *" required="1"
                                   options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_partner_source_id', '!=', False)]}"/>
                            <field name="th_collaborative_products_ids" string="Sản phẩm hợp tác *"
                                   attrs="{'required': [('th_first_status_stage', '!=', True)], 'readonly': [('th_pom_lead_count', '>', 0), ('th_collaborative_products_ids', '!=', False)]}"
                                   widget="many2many_tags"
                                   options="{'no_create': True, 'color_field': 'th_color'}"/>
                            <field name="th_call_status" string="Tình trạng gọi *"
                                   options="{'no_create': True, 'no_open': True}" required="1"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_call_status', '!=', False)]}"/>
                            <field name="th_call_status_detail_id" string="Trạng thái chi tiết *"
                                   options="{'no_create': True, 'no_open': True}"
                                   attrs="{'invisible': [('th_call_status', '=', False)], 'readonly': [('th_pom_lead_count', '>', 0), ('th_call_status_detail_id', '!=', False)]}"
                                   required="True"/>
                            <field name="th_city_id"
                                   string="Tỉnh/Thành phố *"
                                   options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0)],
                                           'required': [('th_stage_need_document', '=', True)]}"/>
                            <field name="th_district_id"
                                   options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0)], 'invisible': [('th_city_id', '=', False)]}"/>
                            <field name="th_ward_id"
                                   options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0)], 'invisible': [('th_district_id', '=', False)]}"/>
                            <field name="th_work_unit"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0)]}"/>
                        </group>
                        <group>
                            <field name="th_commission_policy" options="{'no_open': True, 'no_create': True}"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_commission_policy', '!=', False)]}"/>
                            <field name="th_contract_status"
                                   string="Trạng thái hợp đồng *"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0)],
                                           'required': [('th_stage_need_document', '=', True)]}"/>
                            <field name="th_contract_number"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_contract_number', '!=', False)],
                                           'invisible': [('th_contract_status', '!=', 'signed')]}"/>
                            <field name="th_contract_sign_date"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_contract_sign_date', '!=', False)],
                                           'invisible': [('th_contract_status', '!=', 'signed')]}"/>
                            <field name="th_contract_file" widget="many2many_binary"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_contract_file', '!=', False)],
                                           'invisible': [('th_contract_status', '!=', 'signed')]}"/>
                            <field name="th_personal_document" widget="many2many_binary"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_personal_document', '!=', False)]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="description" string="Mô tả">
                            <field name="th_description"
                                   attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_description', '!=', False)]}"/>
                        </page>
                        <page name="extra_info" string="Thông tin bổ sung">
                            <group>
                                <group>
                                    <field name="th_partner_reference_id"
                                           options="{'no_open': True, 'no_create': True}"
                                           attrs="{'readonly': ['|',('th_check_partner_referred', '=', True), ('th_pom_lead_count', '>', 0)]}"/>
                                    <field name="th_check_partner_referred" invisible="1"/>
                                    <field name="th_duplicate_lead_formio" invisible="1"/>
                                    <field name="th_affiliate_marketing_code"/>
                                    <field name="create_date"/>
                                    <field name="th_registration_date"/>
                                    <field name="th_source_creator"
                                           options="{'no_open': True, 'no_create': True}"/>
                                    <field name="th_date_duplicate_lead"
                                           attrs="{'invisible': [('th_duplicate_lead_formio', '=', False)], 'required': [('th_duplicate_lead_formio', '!=', False)]}"/>
                                </group>
                                <group>
                                    <field name="th_date_to_level_up_p4" readonly="1"/>
                                    <field name="th_date_to_level_up_p5" readonly="1"/>
                                    <field name="th_date_to_level_up_p6" readonly="1"/>
                                    <field name="th_reuse" attrs="{'readonly': [('th_pom_lead_count', '>', 0)]}"/>
                                </group>
                            </group>
                        </page>
                        <page name="" string="Ngân hàng">
                            <field name="th_bank_ids">
                                <tree no_open="1">
                                    <field name="sequence" widget="handle"/>
                                    <field name="bank_id" required="1" string="Ngân hàng"/>
                                    <!--                                    <field name="partner_id" invisible="1"/>-->
                                    <!--                                    <field name="acc_holder_name"/>-->
                                    <field name="acc_number" string="Số tài khoản"/>
                                    <!--                                    <field name="th_branch_name"/>-->
                                </tree>
                            </field>
                        </page>
                        <page name="" string="Data getfly">
                            <group>
                                <field name="th_data_getfly" readonly="1"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids" options="{'post_refresh': 'recipients'}"/>
                </div>
            </form>
        </field>
    </record>

    <record id="prm_lead_view_tree" model="ir.ui.view">
        <field name="name">prm.lead.tree</field>
        <field name="model">prm.lead</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <tree multi_edit="1">
                <field name="th_last_status_stage" invisible="1"/>
                <field name="th_pom_lead_count" invisible="1"/>
                <field name="th_storage" invisible="1"/>
                <field name="name" readonly="1" optional="hide" widget="tree_url"/>
                <field name="th_partner_code" widget="tree_url" />
                <field name="th_customer_code_aum" widget="tree_url" invisible="1"/>
                <field name="th_reuse_source" optional="hide" readonly="1" widget="tree_url"/>
                <field name="th_partner_id" optional="show" options="{'no_open': True, 'no_quick_create': True}"
                       required="1" attrs="{'readonly': [('id', '!=', False)]}"/>
                <field name="th_ownership_unit_id" readonly="1" options="{'no_create': True, 'no_open': True}"/>
                <field name="th_user_id" optional="show" options="{'no_create': True}" readonly="1"/>
                <field name="th_partner_phone" optional="show"/>
                <field name="th_partner_phone2" readonly="1" optional="show"/>
                <field name="th_partner_email" optional="show"/>
                <field name="th_contract_number"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_contract_number', '!=', False)]}"
                       optional="hide"/>
                <field name="th_contract_sign_date"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_contract_sign_date', '!=', False)]}"
                       optional="hide"/>
                <field name="th_call_status" optional="show" options="{'no_create': True, 'no_open': True}"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_call_status', '!=', False)]}"
                       required="1"/>
                <field name="th_call_status_detail_id" optional="show" options="{'no_create': True, 'no_open': True}"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_call_status_detail_id', '!=', False)]}"
                       required="1"/>
                <field name="th_stage_id" optional="show" options="{'no_create': True, 'no_open': True}"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_stage_id', '!=', False)]}"/>
                <field name="th_reuse" optional="hide"/>
                <field name="th_partner_group_ids" widget="many2many_tags" optional="show" required="1"
                       options="{'no_create': True, 'color_field': 'th_color'}"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_partner_group_ids', '!=', False)]}"/>
                <field name="th_work_unit" optional="hide"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_work_unit', '!=', False)]}"/>
                <field name="th_city_id" optional="show"/>
                <field name="th_collaborative_products_ids" widget="many2many_tags" optional="hide"
                       options="{'no_create': True, 'color_field': 'th_color'}" required="1"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_collaborative_products_ids', '!=', False)]}"/>
                <field name="th_partner_source_id" optional="hide" required="1"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_partner_source_id', '!=', False)]}"/>
                <field name="th_last_check" widget="remaining_days" optional="show" readonly="1"
                       attrs="{'invisible': [('th_pom_lead_count', '=', 1)]}"/>
                <field name="th_description" optional="show"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_description', '!=', False)]}"/>
                <field name="th_partner_reference_id" optional="hide"
                       attrs="{'readonly': [('th_pom_lead_count', '>', 0), ('th_partner_reference_id', '!=', False)]}"/>
                <field name="create_date" optional="hide"/>
                <field name="th_is_pom" optional="hide"/>
                <button name="action_change_state_to_pom" string="Chuyển pom" type="object" class="oe_highlight"
                        attrs="{'invisible': ['|', '|', ('th_storage', '=', True), ('th_last_status_stage', '!=', True), ('th_pom_lead_count', '!=', 0)]}"/>
            </tree>
        </field>
    </record>

    <record id="prm_lead_archives_view_tree" model="ir.ui.view">
        <field name="name">prm.lead.archives.tree</field>
        <field name="model">prm.lead</field>
        <field name="inherit_id" ref="prm_lead_view_tree"/>
        <field name="mode">primary</field>
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="js_class">prm_lead_list</attribute>
            </xpath>
        </field>
    </record>

    <record id="action_prm_lead_view" model="ir.actions.act_window">
        <field name="name">PRM</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">prm.lead</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'create': True, 'default_th_ownership_unit_id': active_id}</field>
        <field name="view_id" ref="prm_lead_archives_view_tree"/>
        <field name="domain">[('th_type', '=', 'prm'), ('th_storage', '=', False), ('th_ownership_unit_id',
            '=',active_id)]
        </field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Chưa có dữ liệu
            </p>
        </field>
    </record>
</odoo>