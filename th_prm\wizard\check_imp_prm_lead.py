import xlsxwriter
from odoo import models, fields, _
import xlrd
from odoo.exceptions import UserError
import base64
from io import BytesIO
import openpyxl


class CheckImpPrmLead(models.TransientModel):
    _name = "th.check.imp.prm.lead"
    _description = "<PERSON><PERSON><PERSON> tra cơ hội trước khi import"

    file = fields.Binary(string="File Excel")
    file_name = fields.Char(string="Tên File")

    def check_imp_prm(self):
        if not self.file:
            raise UserError("Vui lòng tải lên tập tin của bạn !!!")
        try:
            decoded_data = base64.decodebytes(self.file)
            workbook = openpyxl.load_workbook(BytesIO(decoded_data), data_only=True)
        except Exception:
            raise UserError('Tính năng chỉ hỗ trợ các tập tin dạng excel !!!')

        sheet = workbook.active
        duplicate_rows = []

        # Lấy tiêu đề cột
        headers = [cell.value for cell in sheet[1]]

        for row_idx in range(2, sheet.max_row + 1):
            row_values = [cell.value for cell in sheet[row_idx]]
            phone_number = row_values[0]
            if not phone_number:
                continue

            if self.env['prm.lead'].search(
                    ['|', ('th_partner_phone', '=', phone_number), ('th_partner_phone2', '=', phone_number)]):
                # Thêm toàn bộ dòng dữ liệu có số điện thoại trùng vào danh sách
                duplicate_rows.append(row_values)

        if duplicate_rows:
            # Tạo file Excel chứa các dòng dữ liệu trùng lặp
            output = BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            worksheet = workbook.add_worksheet('Duplicate Rows')

            # Ghi tiêu đề cột vào file Excel
            for col_idx, header in enumerate(headers):
                worksheet.write(0, col_idx, header)
                worksheet.write(0, col_idx + 1, 'Mối quan hệ')
                worksheet.write(0, col_idx + 2, 'Tình trạng gọi')
                worksheet.write(0, col_idx + 3, 'Người phụ trách')

            # Ghi các dòng trùng lặp vào file Excel
            for row_idx, row_data in enumerate(duplicate_rows, 1):
                for col_idx, cell_value in enumerate(row_data):
                    lead = self.env['prm.lead'].search(
                        ['|', ('th_partner_phone', '=', cell_value), ('th_partner_phone2', '=', cell_value)])
                    if len(lead) == 1:
                        worksheet.write(row_idx, col_idx, cell_value)
                        worksheet.write(row_idx, col_idx + 1, lead.th_stage_id.name if lead.th_stage_id else '')
                        worksheet.write(row_idx, col_idx + 2, lead.th_call_status.name if lead.th_call_status else '')
                        worksheet.write(row_idx, col_idx + 3, lead.th_user_id.name if lead.th_user_id else '')
                    else:
                        raise UserError('Trong file có số điện thoại %s đang tồn tại nhiều cơ hội trên hệ thống!' % cell_value)

            workbook.close()
            output.seek(0)
            excel_file = output.read()
            output.close()

            # Gán file vào trường để tải xuống tự động
            self.write({
                'file': base64.encodebytes(excel_file),
                'file_name': 'danh_sach_trung_lap.xlsx'
            })

            return {
                'type': 'ir.actions.act_url',
                'url': '/web/content?model=th.check.imp.prm.lead&id=%d&field=file&filename=danh_sach_trung_lap.xlsx&download=true' % self.id,
                'target': 'self',
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _(
                        'Không có số điện thoại nào bị trùng trên hệ thống'),
                    'message': '',
                    'type': 'info',
                    'sticky': False,
                    'next': {'type': 'ir.actions.act_window_close'}
                }
            }
