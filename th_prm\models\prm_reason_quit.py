from odoo import fields, models, exceptions, api


class PRMReasonQuit(models.Model):
    _name = "prm.reason.quit"
    _description = "<PERSON>ý do ngừng hợp tác"

    name = fields.Char(string="<PERSON>ại", required=True)
    th_reason_quit_detail_ids = fields.One2many(comodel_name="prm.reason.quit.detail", inverse_name="th_reason_quit_id", string="Chi tiết")

    @api.constrains('name')
    def _check_name_uniq(self):
        for rec in self:
            if rec.name and self.search([('name', '=', rec.name), ('id', '!=', rec.id)], limit=1):
                raise exceptions.ValidationError("Tên %s đã tồn tại." %rec.name)
