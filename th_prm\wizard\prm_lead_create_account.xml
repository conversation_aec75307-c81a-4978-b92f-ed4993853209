<odoo>

    <!-- wizard action on res.partner -->
    <record id="th_partner_wizard_action_create_and_open" model="ir.actions.server">
        <field name="name"><PERSON><PERSON><PERSON> quyền truy cập <PERSON></field>
        <field name="model_id" ref="portal.model_portal_wizard"/>
        <field name="binding_model_id" ref="th_prm.model_prm_lead"/>
        <field name="state">code</field>
        <field name="code">action = model.action_open_wizard()</field>
    </record>

    <record id="th_partner_wizard_action" model="ir.actions.act_window">
        <field name="name"><PERSON><PERSON><PERSON> quyền truy cập Portal</field>
        <field name="res_model">portal.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="binding_model_id" eval="False"/>
    </record>


</odoo>