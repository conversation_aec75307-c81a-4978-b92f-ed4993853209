<odoo>
    <record id="th_view_product_image_tree" model="ir.ui.view">
         <field name="name">th.product.image.view.tree</field>
        <field name="model">th.product.image</field>
        <field name="arch" type="xml">
            <tree>
                <field name="name" string="Tên hình <PERSON>"/>
                <field name="image_1920" string="K<PERSON>ch thước hình <PERSON>" readonly="1"/>
            </tree>
        </field>
    </record>
    <record id="th_view_product_image_form" model="ir.ui.view">
        <field name="name">th.product.image.view.form</field>
        <field name="model">th.product.image</field>
        <field name="arch" type="xml">
            <form string="Product Images">
                <field name="sequence" invisible="1"/>
                <div class="row o_website_sale_image_modal">
                    <div class="col-md-6 col-xl-5">
                        <label for="image_1920" string="<PERSON><PERSON><PERSON>"/>
                        <field name="image_1920" filename="name" readonly="1"/>
                    </div>
                    <div class="col-md-6 col-xl-7 text-center o_website_sale_image_modal_container">
                        <div class="row">
                            <div class="col">
                                <field name="image_1920" widget="image"/>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </field>
    </record>
    <record id="th_view_product_image_kanban" model="ir.ui.view">
        <field name="name">th.product.image.view.kanban</field>
        <field name="model">th.product.image</field>
        <field name="arch" type="xml">
            <kanban string="Ảnh sản phẩm">
                <field name="id"/>
                <field name="name"/>
                <field name="image_1920"/>
                <field name="sequence" widget="handle"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="card oe_kanban_global_click p-0" style="width:100px; height:100px">
                            <div class="o_squared_image">
                                <img class="card-img-top"
                                     t-att-src="kanban_image('th.product.image', 'image_1920', record.id.raw_value)"
                                     t-att-alt="record.name.value"/>
                            </div>
                            <div class="card-body p-0 pb-1">
<!--                                <h4 class="card-title p-2 m-0 bg-200">-->
<!--                                    <small>-->
<!--                                        <field name="name"/>-->
<!--                                    </small>-->
<!--                                </h4>-->
                            </div>
<!--                             below 100 Kb: good-->
<!--                            <t t-if="record.image_1920.raw_value.length &lt; 100*1000">-->
<!--                                <t t-set="size_status" t-value="'text-bg-success'"/>-->
<!--                                <t t-set="message">Acceptable file size</t>-->
<!--                            </t>-->
<!--                             below 1000 Kb: decent-->
<!--                            <t t-elif="record.image_1920.raw_value.length &lt; 1000*1000">-->
<!--                                <t t-set="size_status" t-value="'text-bg-warning'"/>-->
<!--                                <t t-set="message">Huge file size. The image should be-->
<!--                                    optimized/reduced.-->
<!--                                </t>-->
<!--                            </t>-->
<!--                             above 1000 Kb: bad-->
<!--                            <t t-else="1">-->
<!--                                <t t-set="size_status" t-value="'text-bg-danger'"/>-->
<!--                                <t t-set="message">Optimization required! Reduce the-->
<!--                                    image size or-->
<!--                                    increase your compression settings.-->
<!--                                </t>-->
<!--                            </t>-->
<!--                            <span t-attf-class="badge #{size_status} o_product_image_size"-->
<!--                                  t-esc="record.image_1920.value"-->
<!--                                  t-att-title="message"/>-->
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
</odoo>