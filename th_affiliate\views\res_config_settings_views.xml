<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="th_res_config_settings_view_form" model="ir.ui.view">
        <field name="name">th.res.config.settings.view.form.inherit.my.addon</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="0"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div class="app_settings_block" data-string="Affiliate" string="Affiliate" data-key="th_affiliate">
                    <div id="my_addon_container">
                        <h2>Settings</h2>
                        <div class="row mt16 o_settings_container" name="vps_setting_container">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane"/>
                                <div class="o_setting_right_pane">
                                    <div class="content-group">
                                        <group name="general" string="Cookie">
                                            <label for="th_access_interval_number" string="Thời gian tồn tại:"/>
                                            <div>
                                                <field name="th_access_interval_number" class="oe_inline"/>
                                                <field name="th_access_interval_type" class="oe_inline"
                                                       style="margin-left:3px"/>
                                            </div>
                                        </group>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>

     <record id="th_action_aff_config_settings" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'th_affiliate', 'bin_size': False}</field>
    </record>

</odoo>
