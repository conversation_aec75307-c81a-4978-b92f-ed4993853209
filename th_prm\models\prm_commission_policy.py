from odoo import fields, models, api, exceptions


class PRMCommissionPolicy(models.Model):
    _name = "prm.commission.policy"
    _description = "<PERSON><PERSON><PERSON> s<PERSON>ch hợp tác"

    name = fields.Char(string="Tên", required=True)
    th_description = fields.Char(string="<PERSON>ô tả")

    @api.constrains('name')
    def _check_name_uniq(self):
        for rec in self:
            if self.name and self.search([('name', '=', rec.name), ('id', '!=', rec.id)], limit=1):
                raise exceptions.ValidationError("Tên %s đã tồn tại." % rec.name)