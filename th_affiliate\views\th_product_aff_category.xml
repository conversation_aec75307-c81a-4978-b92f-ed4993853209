<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="th_view_product_aff_category_tree" model="ir.ui.view">
        <field name="name">th.view.product.aff.category.tree</field>
        <field name="model">th.product.aff.category</field>
        <field name="arch" type="xml">
            <tree string="" create="0" edit="0" delete="0">
                <field name="display_name" string="Nhóm sản phẩm"/>
            </tree>
        </field>
    </record>

    <record id="th_view_product_aff_category_form" model="ir.ui.view">
        <field name="name">th.view.product.aff.category.form</field>
        <field name="model">th.product.aff.category</field>
        <field name="arch" type="xml">
            <form string="Form" create="0" edit="0" delete="0">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="th_parent_id"/>
                        <field name="company_id" invisible="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="th_view_product_aff_category_action" model="ir.actions.act_window">
        <field name="name">Nhóm sản phẩm</field>
        <field name="res_model">th.product.aff.category</field>
        <field name="view_mode">tree,form</field>
    </record>


</odoo>