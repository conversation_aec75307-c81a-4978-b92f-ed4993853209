<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="th_prm_mail_template" model="mail.template">
            <field name="name">Thông báo về cơ hội trùng</field>
            <field name="model_id" ref="th_prm.model_prm_lead"/>
            <field name="email_from">{{ ctx.get('email_from', '') }}</field>
            <field name="email_to">{{ ctx.get('email_to', '') }}</field>
            <field name="subject">Thông báo về cơ hội trùng</field>
            <field name="auto_delete" eval="False"/>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p><PERSON><PERSON><PERSON> gử<PERSON>,
                        <t t-out="ctx.get('receiver_name')"/>
                    </p>
                    <p>Bạn c<PERSON> một cơ hội trùng, đố<PERSON> tác đã điền thông tin vào form và muốn đư<PERSON>c chăm sóc trở lại. <PERSON><PERSON> lý
                        thông qua hệ thống ERP hoặc liên kết dưới:
                    </p>
                    <a>
                        <t t-out="ctx.get('url')"/>
                    </a>
                    <br/>
                    <br/>
                    <p>Nội dung mô tả của đối tác:</p>
                    <p> <t t-out="ctx.get('description')"/></p>
                </div>
            </field>
        </record>
    </data>
</odoo>