<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="module_category_prm_management" model="ir.module.category">
        <field name="name"><PERSON><PERSON><PERSON><PERSON> lý quan hệ đối tác</field>
        <field name="description"><PERSON><PERSON> quyền quản lý PRM</field>
        <field name="sequence">20</field>
    </record>

    <record id="module_category_prm" model="ir.module.category">
        <field name="parent_id" ref="module_category_prm_management"/>
        <field name="name">PRM</field>
        <field name="sequence">1</field>
    </record>

    <record id="group_prm_user" model="res.groups">
        <field name="name">Nhân viên</field>
        <field name="category_id" ref="module_category_prm"/>
    </record>

    <record id="group_prm_leader" model="res.groups">
        <field name="name"><PERSON><PERSON><PERSON><PERSON> lý</field>
        <field name="category_id" ref="module_category_prm"/>
        <field name="implied_ids" eval="[(4, ref('group_prm_user'))]"/>
    </record>

    <record id="group_prm_manager" model="res.groups">
        <field name="name">Trưởng phòng</field>
        <field name="category_id" ref="module_category_prm"/>
        <field name="implied_ids" eval="[(4, ref('group_prm_leader'))]"/>
    </record>

    <record id="group_prm_administrator" model="res.groups">
        <field name="name">Quản trị viên</field>
        <field name="category_id" ref="module_category_prm"/>
        <field name="implied_ids" eval="[(4, ref('group_prm_manager'))]"/>
    </record>

    <record id="prm_lead_group_user_rule" model="ir.rule">
        <field name="name">prm_lead_group_user_rule</field>
        <field name="model_id" ref="model_prm_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_user'))]"/>
        <field name="domain_force">[('th_user_id', '=', user.id)]
        </field>
    </record>

    <record id="prm_lead_group_leader_rule" model="ir.rule">
        <field name="name">prm_lead_group_leader_rule</field>
        <field name="model_id" ref="model_prm_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_leader'))]"/>
        <field name="domain_force">[ '&amp;', '|', ('th_user_id', '=', user.id), ('th_team_leader_ids', 'in', user.ids),
            '|', ('th_ownership_unit_id.th_team_prm.manager_id', '=', user.id),
            ('th_ownership_unit_id.th_team_prm.th_member_ids', 'in', user.ids)]
        </field>
    </record>

    <record id="prm_lead_group_manager_rule" model="ir.rule">
        <field name="name">prm_lead_group_manager_rule</field>
        <field name="model_id" ref="model_prm_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_manager'))]"/>
        <field name="domain_force"></field>
    </record>

    <record id="prm_lead_group_admin_rule" model="ir.rule">
        <field name="name">prm_lead_group_admin_rule</field>
        <field name="model_id" ref="model_prm_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_administrator'))]"/>
    </record>

    <record id="pom_lead_group_user_rule" model="ir.rule">
        <field name="name">pom_lead_group_user_rule</field>
        <field name="model_id" ref="model_pom_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_user'))]"/>
        <field name="domain_force">[('th_user_id', '=', user.id)]
        </field>
    </record>

    <record id="pom_lead_group_leader_rule" model="ir.rule">
        <field name="name">pom_lead_group_leader_rule</field>
        <field name="model_id" ref="model_pom_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_leader'))]"/>
        <field name="domain_force">[ '&amp;', '|', ('th_user_id', '=', user.id), ('th_team_leader_ids', 'in', user.ids),
            '|', ('th_ownership_unit_id.th_team_prm.manager_id', '=', user.id),
            ('th_ownership_unit_id.th_team_prm.th_member_ids', 'in', user.ids)]
        </field>
    </record>

    <record id="pom_lead_group_manager_rule" model="ir.rule">
        <field name="name">pom_lead_group_manager_rule</field>
        <field name="model_id" ref="model_pom_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_manager'))]"/>
        <field name="domain_force"></field>
    </record>

    <record id="pom_lead_group_admin_rule" model="ir.rule">
        <field name="name">pom_lead_group_admin_rule</field>
        <field name="model_id" ref="model_pom_lead"/>
        <field name="groups" eval="[(4, ref('group_prm_administrator'))]"/>
    </record>

</odoo>