==============
Tổng quan
==============

A. <PERSON><PERSON> tả module
============================
PRM (Partner Relationship Management) dùng để quản lý quan hệ đối tác.
Đối tác của PRM là những đối tượng khách hàng có thể là cá nhân hoặc tổ chức mà có tiềm năng,
cơ hội trở thành đối tác của người sử dụng (cá nhân hoặc tổ chức người sử dụng) trong tương lai.

POM (Partner Operating Management) dùng để quản lý điều hành đối tác.
Đối tác của POM là những đối tượng khách hàng PRM sau khi đã trở thành đối tác của người sử dụng.

B. Đ<PERSON><PERSON> tượng sử dụng
============================
1. Nhân viên Phát triển mạng lưới
2. Nhân viên Marketing

C. Phụ thuộc (các module liên quan)
============================
1. th_contact
2. portal
3. th_setup_parameters
4. web_domain_field

D. Chức năng chính
============================
1. Tạo cơ hội
2. Chuyển POM
3. Tạo tài khoản Affiliate
4. Giao cơ hội
5. Thu hồi cơ hội về kho
6. Tái sử dụng cơ hội từ kho
7. Làm sạch dữ liệu import
8. Cấu hình

E. Các quyền trong module
============================
1. Nhân viên
2. Quản lý
3. Trưởng phòng
4. Quản trị viên
